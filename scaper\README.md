# MCP Web Scraper with Image Analysis

一个基于 FastMCP 的通用网页爬虫服务器，具备图片内容分析功能。

## 功能特性

- 🕷️ **通用网页爬虫**: 使用 Playwright 爬取网页内容
- 🖼️ **图片解读**: 集成火山引擎豆包模型，智能分析图片内容
- 🚀 **MCP SSE 协议**: 基于 FastMCP 框架，支持 Server-Sent Events
- ⚡ **异步处理**: 高性能异步架构
- 🔧 **简洁高效**: 单一 MCP 接口，代码简洁

## 安装和配置

### 1. 环境要求

- Python 3.9+
- uv (Python 包管理器)

### 2. 安装依赖

```bash
# 使用 uv 创建虚拟环境并安装依赖
uv sync

# 安装 Playwright 浏览器
uv run playwright install chromium
```

### 3. 配置环境变量

复制环境变量模板：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：

```env
# 火山引擎豆包模型配置
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DOUBAO_MODEL=doubao-pro-4k

# MCP 服务器配置
MCP_SERVER_HOST=127.0.0.1
MCP_SERVER_PORT=8000
```

## 使用方法

### 启动服务器

```bash
# 直接运行
uv run python server.py

# 或使用 FastMCP CLI
uv run fastmcp run server.py --transport sse --port 8000
```

### MCP 工具接口

服务器提供以下 MCP 工具：

#### 1. `scrape_webpage_tool`

爬取网页内容，可选择是否分析图片

**参数:**

- `url` (str): 要爬取的网页 URL
- `include_images` (bool): 是否包含图片信息，默认 True
- `analyze_images` (bool): 是否分析图片内容，默认 False
- `image_analysis_prompt` (str): 图片分析提示词

**返回:** 包含网页标题、文本内容、链接、图片信息和分析结果的字典

#### 2. `analyze_webpage_images`

专门分析网页中的所有图片

**参数:**

- `url` (str): 要分析的网页 URL
- `analysis_prompt` (str): 图片分析提示词

**返回:** 包含所有图片分析结果的详细信息

#### 3. `analyze_image_url`

分析指定 URL 的单张图片

**参数:**

- `image_url` (str): 图片的 URL 地址
- `analysis_prompt` (str): 分析提示词

**返回:** 图片分析结果

#### 4. `get_server_status`

获取服务器状态和配置信息

**返回:** 服务器健康状态、功能列表和配置信息

## 快速开始

### 1. 启动服务器

```bash
# 方法1: 直接运行
uv run python server.py

# 方法2: 使用启动脚本
uv run python start.py

# 方法3: 使用 FastMCP CLI
uv run fastmcp run server.py --transport sse --port 8000
```

服务器将在 `http://127.0.0.1:8000/sse` 启动

### 2. 测试连接

```bash
# 测试基础功能
uv run python test_basic.py

# 测试客户端连接
uv run python test_client.py
```

## 使用示例

### 基础网页爬取

```python
from fastmcp import Client
import asyncio

async def main():
    client = Client("http://127.0.0.1:8000/sse")
    async with client:
        result = await client.call_tool("scrape_webpage_tool", {
            "url": "https://example.com",
            "include_images": True,
            "analyze_images": False
        })
        print(result[0].text)

asyncio.run(main())
```

### 网页爬取 + 图片分析

```python
# 爬取网页并分析所有图片（需要配置 API 密钥）
result = await client.call_tool("scrape_webpage_tool", {
    "url": "https://example.com",
    "include_images": True,
    "analyze_images": True,
    "image_analysis_prompt": "请详细描述图片内容，包括主要对象和场景"
})
```

### 专门的图片分析

```python
# 只分析网页中的图片
result = await client.call_tool("analyze_webpage_images", {
    "url": "https://example.com",
    "analysis_prompt": "分析图片中的商品信息"
})
```

### 单张图片分析

```python
# 分析指定图片
result = await client.call_tool("analyze_image_url", {
    "image_url": "https://example.com/image.jpg",
    "analysis_prompt": "这张图片展示了什么内容？"
})
```

### 服务器状态检查

```python
# 获取服务器状态
result = await client.call_tool("get_server_status", {})
print(result[0].text)
```

## 技术架构

- **FastMCP**: MCP 服务器框架
- **Playwright**: 网页自动化和爬取
- **火山引擎豆包**: 图片内容分析
- **aiohttp**: 异步 HTTP 客户端
- **BeautifulSoup**: HTML 解析
- **PIL**: 图片处理

## 开发和测试

```bash
# 运行测试
uv run pytest

# 代码格式化
uv run black .
uv run isort .
```

## 注意事项

1. 确保已正确配置火山引擎豆包模型的 API 密钥
2. 网页爬取时请遵守目标网站的 robots.txt 和使用条款
3. 图片分析功能需要网络连接到火山引擎 API
4. 大量图片分析可能产生 API 费用，请注意控制使用量

## 许可证

MIT License
