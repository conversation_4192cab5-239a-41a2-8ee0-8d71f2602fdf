#!/usr/bin/env python3
"""启动脚本 - 用于启动 MCP 服务器"""
import sys
import os
import subprocess
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_environment():
    """检查环境配置"""
    logger.info("检查环境配置...")
    
    # 检查 .env 文件
    if not os.path.exists('.env'):
        logger.warning(".env 文件不存在，请复制 .env.example 并配置")
        return False
    
    # 检查关键环境变量
    from config import config
    try:
        config.validate()
        logger.info("环境配置验证通过")
        return True
    except ValueError as e:
        logger.error(f"环境配置验证失败: {e}")
        return False


def install_playwright():
    """安装 Playwright 浏览器"""
    logger.info("检查 Playwright 浏览器...")
    try:
        result = subprocess.run(
            ["playwright", "install", "chromium"],
            capture_output=True,
            text=True,
            check=True
        )
        logger.info("Playwright 浏览器安装/更新完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Playwright 浏览器安装失败: {e}")
        return False
    except FileNotFoundError:
        logger.error("未找到 playwright 命令，请确保已安装依赖")
        return False


def start_server():
    """启动服务器"""
    logger.info("启动 MCP 服务器...")
    
    try:
        # 导入并启动服务器
        from server import mcp, config
        
        logger.info(f"服务器将在 {config.MCP_SERVER_HOST}:{config.MCP_SERVER_PORT} 启动")
        logger.info("按 Ctrl+C 停止服务器")
        
        mcp.run(
            transport="sse",
            host=config.MCP_SERVER_HOST,
            port=config.MCP_SERVER_PORT
        )
        
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    logger.info("=== MCP Web Scraper 启动器 ===")
    
    # 检查环境
    if not check_environment():
        logger.error("环境检查失败，请检查配置")
        sys.exit(1)
    
    # 安装 Playwright 浏览器
    if not install_playwright():
        logger.warning("Playwright 浏览器安装失败，但将尝试继续启动")
    
    # 启动服务器
    start_server()


if __name__ == "__main__":
    main()
