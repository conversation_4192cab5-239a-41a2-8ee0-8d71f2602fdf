"""MCP SSE 服务器 - 通用网页爬虫与图片解读"""
import asyncio
import logging
from typing import Dict, Any, List, Optional

from fastmcp import FastMCP
from config import config
from scraper import scrape_webpage
from image_analyzer import image_analyzer

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 MCP 服务器实例
mcp = FastMCP(
    name="Web Scraper with Image Analysis",
    instructions="""
    这是一个通用网页爬虫服务器，具备以下功能：
    1. 爬取网页内容（文本、链接、图片）
    2. 使用火山引擎豆包模型分析图片内容
    3. 支持批量处理和自定义分析提示词
    
    主要工具：
    - scrape_webpage: 爬取指定网页的内容
    - analyze_webpage_images: 专门分析网页中的图片
    - analyze_image_url: 分析指定URL的单张图片
    """
)


@mcp.tool()
async def scrape_webpage_tool(
    url: str,
    include_images: bool = True,
    analyze_images: bool = False,
    image_analysis_prompt: str = "请详细描述这张图片的内容，包括主要元素、颜色、场景等"
) -> Dict[str, Any]:
    """
    爬取网页内容，可选择是否分析图片
    
    Args:
        url: 要爬取的网页URL
        include_images: 是否包含图片信息（默认True）
        analyze_images: 是否使用AI分析图片内容（默认False）
        image_analysis_prompt: 图片分析的提示词
        
    Returns:
        包含网页内容的详细信息
    """
    try:
        logger.info(f"开始爬取网页: {url}")
        
        result = await scrape_webpage(
            url=url,
            include_images=include_images,
            analyze_images=analyze_images,
            image_analysis_prompt=image_analysis_prompt
        )
        
        logger.info(f"网页爬取完成: {url}, 状态: {result.get('status')}")
        return result
        
    except Exception as e:
        logger.error(f"网页爬取失败: {url}, 错误: {str(e)}")
        return {
            "url": url,
            "status": "error",
            "error": f"爬取失败: {str(e)}"
        }


@mcp.tool()
async def analyze_webpage_images(
    url: str,
    analysis_prompt: str = "请详细描述这张图片的内容，包括主要对象、场景、颜色和任何文字信息"
) -> Dict[str, Any]:
    """
    专门用于分析网页中所有图片的工具
    
    Args:
        url: 要分析的网页URL
        analysis_prompt: 图片分析提示词
        
    Returns:
        包含所有图片分析结果的信息
    """
    try:
        logger.info(f"开始分析网页图片: {url}")
        
        # 先爬取网页获取图片信息
        scrape_result = await scrape_webpage(
            url=url,
            include_images=True,
            analyze_images=True,
            image_analysis_prompt=analysis_prompt
        )
        
        if scrape_result.get("status") == "error":
            return scrape_result
        
        # 提取图片分析结果
        image_analyses = scrape_result.get("image_analyses", [])
        images_info = scrape_result.get("images", [])
        
        result = {
            "url": url,
            "title": scrape_result.get("title", ""),
            "total_images": len(images_info),
            "analyzed_images": len(image_analyses),
            "image_analyses": image_analyses,
            "status": "success"
        }
        
        logger.info(f"图片分析完成: {url}, 分析了 {len(image_analyses)} 张图片")
        return result
        
    except Exception as e:
        logger.error(f"图片分析失败: {url}, 错误: {str(e)}")
        return {
            "url": url,
            "status": "error",
            "error": f"图片分析失败: {str(e)}"
        }


@mcp.tool()
async def analyze_image_url(
    image_url: str,
    analysis_prompt: str = "请详细描述这张图片的内容"
) -> Dict[str, Any]:
    """
    分析指定URL的单张图片
    
    Args:
        image_url: 图片的URL地址
        analysis_prompt: 分析提示词
        
    Returns:
        图片分析结果
    """
    try:
        logger.info(f"开始分析图片: {image_url}")
        
        # 下载图片
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status != 200:
                    return {
                        "image_url": image_url,
                        "status": "error",
                        "error": f"无法下载图片，HTTP状态码: {response.status}"
                    }
                
                image_data = await response.read()
        
        # 分析图片
        analysis_result = await image_analyzer.analyze_image(image_data, analysis_prompt)
        
        result = {
            "image_url": image_url,
            "analysis": analysis_result,
            "status": "success"
        }
        
        logger.info(f"图片分析完成: {image_url}")
        return result
        
    except Exception as e:
        logger.error(f"图片分析失败: {image_url}, 错误: {str(e)}")
        return {
            "image_url": image_url,
            "status": "error",
            "error": f"分析失败: {str(e)}"
        }


@mcp.tool()
async def get_server_status() -> Dict[str, Any]:
    """
    获取服务器状态信息
    
    Returns:
        服务器状态和配置信息
    """
    try:
        # 验证配置
        config.validate()
        
        return {
            "status": "healthy",
            "server_name": "Web Scraper with Image Analysis",
            "version": "1.0.0",
            "features": [
                "网页内容爬取",
                "图片信息提取", 
                "AI图片内容分析",
                "批量图片处理"
            ],
            "config": {
                "model": config.DOUBAO_MODEL,
                "headless_browser": config.PLAYWRIGHT_HEADLESS,
                "timeout": config.PLAYWRIGHT_TIMEOUT
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


if __name__ == "__main__":
    try:
        # 验证配置
        config.validate()
        logger.info("配置验证通过")
        
        # 启动服务器
        logger.info(f"启动 MCP SSE 服务器，地址: {config.MCP_SERVER_HOST}:{config.MCP_SERVER_PORT}")
        mcp.run(
            transport="sse",
            host=config.MCP_SERVER_HOST,
            port=config.MCP_SERVER_PORT
        )
        
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        raise
